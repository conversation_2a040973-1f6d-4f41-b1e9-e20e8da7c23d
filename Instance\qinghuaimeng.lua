-- 函数：重置情怀梦副本的冷却计时器和别名
local function reset_qinghuaimeng_timer()
    local now_time = get_time()
    var["qinghuaimeng_start_time"] = now_time
    send("alias 挑战情怀梦 " .. now_time)
    log_message("情怀梦副本冷却计时器已重置。", C.G, "<系统>")
  end
  
  -- 函数：根据层数设置技能
  local function setup_qinghuaimeng_skills(level_num)
    local beiskills_var = "qhm_beiskills_" .. level_num
    local pfm_var = "qhm_pfm_" .. level_num
    
    send("alias bei_skills " .. expand(var[beiskills_var]))
    send("alias pfm " .. expand(var[pfm_var]))
    send("set wimpycmd hp " .. var["char_id"] .. "\\pfm")
    send("bei_skills")
  end
  
  -- 函数：在执行回调前，有选择地执行一个 "yun_pfm"
  local function execute_with_optional_yun_pfm(callback_func)
      check_busy2(function()
          if var["yun_pfm"] and var["yun_pfm"] ~= "" then
              exec(var["yun_pfm"])
              check_busy2(function()
                  if callback_func then callback_func() end
              end)
          else
              if callback_func then callback_func() end
          end
      end)
  end
  
  -- 提取一个公共的启动函数
  local function start_first_floor()
    var.qhm_current_floor_killers = 0
    log_message("第一层杀敌计数已重置。", C.Y, "<系统>")
    g(70, function()
        send("set wimpy 100")
        wait(3, function()
            setup_qinghuaimeng_skills(1)
            wait(3, function()
                close_fight()
                exec("qinghuaimeng;up;mazemap")
                log_message("☆第一层开始☆", C.Y, "<系统>")
                exec("hp " .. var["char_id"])
            end)
        end)
    end)
  end
  
  -- 全局变量：存储迷宫数据到内存
  local qhm_maze_data = {
      level1 = nil,  -- 第一层迷宫数据
      level2 = nil,  -- 第二层迷宫数据
      capturing = false,  -- 是否正在捕获数据
      current_level = 0,  -- 当前捕获的层数
      raw_lines = {}  -- 原始捕获的行数据
  }

  -- 函数：清理和格式化迷宫数据
  local function format_maze_content(content, level)
      if level == 1 then
          content = content:gsub("情怀梦境迷宫第一层：", "")
          content = content:gsub("情怀梦境迷宫第一层行走方向提示：(.*)", "")
          content = content:gsub("C:当前位置 B:宝藏位置 E:出口位置", "")
      elseif level == 2 then
          content = content:gsub("情怀梦境迷宫第二层：\n?", "")
          content = content:gsub("情怀梦境迷宫第二层行走方向提示：[^\n]*\n?", "")
          content = content:gsub("C:当前位置 B:宝藏位置 E:出口位置\n?", "")
          content = content:gsub("^\n+", ""):gsub("\n+$", "")
          local formatted_lines = {}
          for line in content:gmatch("[^\n]+") do
              local new_line = ""
              for i = 1, #line do
                  local char = line:sub(i, i)
                  if i == 1 or char ~= " " then
                      new_line = new_line .. char
                  else
                      local is_near_pipe = (i > 1 and line:sub(i - 1, i) == " |") or (i < #line and line:sub(i, i + 1) == "| ")
                      if ((i - 1) % 4 == 0) and not is_near_pipe then
                          new_line = new_line .. ":"
                      else
                          new_line = new_line .. char
                      end
                  end
              end
              table.insert(formatted_lines, new_line)
          end
          content = table.concat(formatted_lines, "\n")
      end
      return content
  end

  -- 函数：将内容解析为二维迷宫数组
  local function parse_content_to_maze(content)
      local maze = {}
      for line in content:gmatch("[^\n]+") do
          local row = {}
          for i = 1, #line do
              table.insert(row, line:sub(i, i))
          end
          table.insert(maze, row)
      end
      return #maze > 0 and maze or nil
  end

  -- 函数：从内存中准备迷宫数据并解析为二维表格
  local function prepare_maze_data_and_parse(level)
      local maze_content

      if level == 1 then
          maze_content = qhm_maze_data.level1
      elseif level == 2 then
          maze_content = qhm_maze_data.level2
      end

      if not maze_content then
          echo("错误：第" .. level .. "层迷宫数据未找到！请确保已正确捕获迷宫地图。")
          return nil
      end

      -- 格式化内容
      local formatted_content = format_maze_content(maze_content, level)

      -- 解析为二维数组
      local maze = parse_content_to_maze(formatted_content)

      if maze then
          log_message("成功从内存加载第" .. level .. "层迷宫数据，大小: " .. #maze .. "x" .. (#maze > 0 and #maze[1] or 0), C.G, "<系统>")
      end

      return maze
  end


-- 函数：获取相反的方向
local function get_opposite_direction(dir)
    local opposites = { north = "south", south = "north", east = "west", west = "east" }
    return opposites[dir]
end


-- 全局的继续移动函数句柄，用于在触发器中恢复移动流程
__qhm_continue_movement = nil

-- 函数：按顺序执行移动指令，并在完成后执行回调
local function execute_maze_movement(directions_to_move, callback_after_all_moves, is_heading_to_exit)
    if not directions_to_move or #directions_to_move == 0 then
        if callback_after_all_moves then callback_after_all_moves() end
        return
    end

    local move_index = 1
    local single_step, killer_check_and_loop, wait_for_enemy_to_disappear

    wait_for_enemy_to_disappear = function(callback_when_clear)
        if not var.qhm_enemy_present then
            if callback_when_clear then callback_when_clear() end
        else
            log_message("等待敌人消失...", C.G, "<系统>")
            wait(1, function() wait_for_enemy_to_disappear(callback_when_clear) end)
        end
    end

    single_step = function()
        -- 寻路完成，执行最终回调并清理
        if move_index > #directions_to_move then
            __qhm_continue_movement = nil -- 清理句柄
            if callback_after_all_moves then callback_after_all_moves() end
            return
        end

        -- 在接近出口时检查杀敌数，如果不够则进入刷怪模式
        if is_heading_to_exit and
           (tonumber(var["qhm_killer_target_num"]) or 0) > 0 and
           #directions_to_move >= 2 and
           move_index == #directions_to_move - 1 then
            
            local current_killers = var.qhm_current_floor_killers or 0
            local target_killers = tonumber(var["qhm_killer_target_num"] or 0)

            if current_killers < target_killers then
                log_message("即将到达出口，但本层杀敌数不足，启动刷怪模式。", C.Y, "<系统>")
                var.is_grinding_killers = true
                killer_check_and_loop()
                return
            end
        end

        -- 正常移动步骤
        wait_for_enemy_to_disappear(function()
            local direction = directions_to_move[move_index]
            check_busy2(function()
                -- 设置继续函数，以便触发器可以调用
                __qhm_continue_movement = single_step
                -- 打开监听“无战斗”的触发器
                open_trigger("otherquest_qinghuaimeng_12")
                
                exec("go " .. direction .. ";kill wanderer")
                move_index = move_index + 1                
            end)
        end)
    end
    
    killer_check_and_loop = function()
        local current_killers = var.qhm_current_floor_killers or 0
        local target_killers = tonumber(var["qhm_killer_target_num"] or 0)

        -- 杀敌数达标，退出刷怪模式，继续寻路
        if current_killers >= target_killers then
            log_message("本层已达到杀敌数量要求 (" .. current_killers .. "/" .. target_killers .. ")，继续前往出口。", C.G, "<系统>")
            var.is_grinding_killers = false
            __qhm_continue_movement = nil
            single_step()
            return
        end
        
        log_message("本层杀敌数未达标 (" .. current_killers .. "/" .. target_killers .. ")，开始来回移动遇敌。", C.Y, "<系统>")
        
        local forward_dir = directions_to_move[#directions_to_move - 1]
        local backward_dir = get_opposite_direction(forward_dir)

        if not forward_dir or not backward_dir then
            log_message("错误：无法确定来回移动的方向。继续正常寻路。", C.R, "<系统>")
            var.is_grinding_killers = false
            __qhm_continue_movement = nil
            single_step()
            return
        end

        -- 定义刷怪的单步移动函数
        local function grind_move(dir, next_func)
             wait_for_enemy_to_disappear(function()
                check_busy2(function()
                    -- 设置继续函数，指向下一步刷怪动作
                    __qhm_continue_movement = next_func
                    open_trigger("otherquest_qinghuaimeng_12")
                    exec("go " .. dir .. ";kill wanderer")
                end)
            end)
        end
        
        -- 定义来回移动的循环
        local move_backward_step
        local move_forward_step = function() grind_move(forward_dir, move_backward_step) end
        move_backward_step = function() grind_move(backward_dir, killer_check_and_loop) end -- 完成一次来回后，重新检查杀敌总数

        -- 开始第一次移动
        move_forward_step()
    end
    
    -- 启动寻路
    single_step()
end

  -- 函数：将A*算法返回的坐标路径转换为方向指令列表
  local function convert_path_to_directions(path)
      if not path or #path < 2 then return {} end
      local directions = {}
      for i = 1, #path - 1 do
          local p_r, p_c = path[i]:match("^(%d+),(%d+)$")
          local c_r, c_c = path[i+1]:match("^(%d+),(%d+)$")
          p_r, p_c, c_r, c_c = tonumber(p_r), tonumber(p_c), tonumber(c_r), tonumber(c_c)
  
          local direction
          if c_r < p_r then direction = "north"
          elseif c_r > p_r then direction = "south"
          elseif c_c < p_c then direction = "west"
          elseif c_c > p_c then direction = "east"
          end
          if direction then table.insert(directions, direction) end
      end
      return directions
  end
  
  -- 函数：将字符坐标转换为单元格坐标
  local function get_cell_coords(r_char, c_char)
    if type(r_char) ~= "number" or type(c_char) ~= "number" then return nil, nil end
    local r_cell = math.floor(r_char / 2)
    local c_cell = math.floor((c_char - 3) / 4) + 1
    return r_cell, c_cell
  end
  
  -- 函数：将单元格坐标转换为字符坐标
  local function get_char_coords(r_cell, c_cell)
    if type(r_cell) ~= "number" or type(c_cell) ~= "number" then return nil, nil end
    local r_char = r_cell * 2
    local c_char = (c_cell - 1) * 4 + 3
    return r_char, c_char
  end
  
  
  add_alias("otherquest_qinghuaimeng", function(params)
    var["killer_master"] = nil
    qinghuaimeng_box = var["qinghuaimeng_box"] or 0
    qinghuaimeng_killer_num_1 = 1    
    open_qinghuaimeng_triggers()
    log_message("☆☆☆"..C.G.."挑战情怀梦副本开始"..C.M.."☆☆☆", C.M, "<解密>")
    set_start_time("情怀梦")
    var["qinghuaimeng_killer_num"] = 0
    var.is_grinding_killers = false
    -- 初始化敌人存在标志和寻路句柄
    var.qhm_enemy_present = false
    __qhm_continue_movement = nil
    var["qhm_killer_target_num"] = var["qhm_killer_target_num"] or 0
    log_message("每层目标杀敌数: " .. var["qhm_killer_target_num"] .. " (设置为0则不启用)", C.Y, "<系统>")

    -- 初始化迷宫数据存储
    qhm_maze_data.level1 = nil
    qhm_maze_data.level2 = nil
    qhm_maze_data.capturing = false
    qhm_maze_data.current_level = 0
    qhm_maze_data.raw_lines = {}
    log_message("迷宫数据存储已初始化", C.G, "<系统>")
  
    if var["qhm_da_huandan"] and var["qhm_da_huandan"] == 1 then
      buy_da_huandan(var["qhm_da_huandan_number"], function()
        close_trigger("otherquest_qinghuaimeng_5")
        check_busy2(function()
          wait(3, start_first_floor)
        end)
      end)
    else
      start_first_floor()
    end
  
    add_trigger("otherquest_qinghuaimeng_2", "s*看起来(.*)想杀死你！$", function(params)
      var["idle"] = 0
      -- 设置敌人存在标志
      var.qhm_enemy_present = true
    end)
  

    add_trigger("otherquest_qinghuaimeng_3", "s*(.*)一个闪身就不见了。$", function(params)
      -- 清除敌人存在标志
      var.qhm_enemy_present = false
    
      var["qinghuaimeng_killer_num"] = (var["qinghuaimeng_killer_num"] or 0) + 1
      var.qhm_current_floor_killers = (var.qhm_current_floor_killers or 0) + 1
    
      if var["killer_master"] then
        var["killer_master_count"] = (var["killer_master_count"] or 0) + 1
        if var["killer_master_count"] == 1 then
          -- 第一个NPC消失，准备挑战下一个
          exec("alias action 准备挑战")
        elseif var["killer_master_count"] == 2 then
          -- 第二个NPC消失，继续走迷宫
          var["killer_master_count"] = nil -- 重置计数器以备下一层
          exec("mazemap")
        end
      end
    
      execute_with_optional_yun_pfm(function()
        exec("yun qi;yun jing;yun jingli")
        -- 战斗结束后，如果正在寻路或刷怪，则恢复流程
        if __qhm_continue_movement then
          local func_to_call = __qhm_continue_movement
          __qhm_continue_movement = nil
          log_message("战斗结束，恢复移动...", C.G, "<系统>")
          func_to_call()
        end
      end)
    end)

  
    add_trigger("otherquest_qinghuaimeng_4", "s*(.*)神志迷糊，脚下一个不稳，倒在地上昏了过去。$", function(params)      
      var.qhm_enemy_present = false
      -- 如果在寻路中昏迷，尝试恢复流程
      if __qhm_continue_movement then
          local func_to_call = __qhm_continue_movement
          __qhm_continue_movement = nil
          log_message("昏迷后恢复，继续移动...", C.Y, "<系统>")
          func_to_call()
      end

      exec("kill wanderer")
    end)
  
    add_trigger("otherquest_qinghuaimeng_6", "s*总步数: 0$", function(params)
      open_trigger("otherquest_qinghuaimeng_11")      
      if var["killer_master"] and var["killer_master"] > 0 then        
        if var["qinghuaimeng_2"] == 1 then
          close_qinghuaimeng_triggers()
          close_trigger("otherquest_qinghuaimeng_6")
          open_trigger("otherquest_qinghuaimeng2_1")
          open_triggers("otherquest_qinghuaimeng", 1, 5)
          open_triggers("otherquest_qinghuaimeng", 7, 18)
          -- 重新开启迷宫数据捕获触发器，为第二层做准备
          open_trigger("otherquest_qinghuaimeng_maze_capture_start2")
          open_trigger("otherquest_qinghuaimeng_maze_capture_end2")
          open_trigger("otherquest_qinghuaimeng_maze_capture_line")
          log_message("第二层迷宫数据捕获触发器已重新启用", C.G, "<系统>")
          var["killer_master"] = nil
          check_busy2(function()
            check_busy(function()              
                setup_qinghuaimeng_skills(1)            
                var.qhm_current_floor_killers = 0
                log_message("第二层杀敌计数已重置。", C.Y, "<系统>")

                exec("up2;mazemap")
                log_message("☆第二层开始☆", C.Y, "<系统>")
            end)
          end)
        else
          check_busy(function()
            exec("leave")
            qinghuaimeng_killer_num=var["qinghuaimeng_killer_num"] - 4
            log_message("挑战情怀梦共杀killer" .. "【" .. qinghuaimeng_killer_num .. "】" .. "个。", C.Y, "<解密>")
            var["qinghuaimeng_killer_num"] = 0
              -- 添加关键全局变量清理
            __qhm_continue_movement = nil
            var.qhm_enemy_present = nil
            var.is_grinding_killers = nil            
            -- 清空迷宫数据但保留结构
            qhm_maze_data.level1 = nil
            qhm_maze_data.level2 = nil
            qhm_maze_data.capturing = false
            qhm_maze_data.current_level = 0
            qhm_maze_data.raw_lines = {}
            local n = collectgarbage("count")
            collectgarbage("collect")
            echo("\n" .. C.x .. "LUA内存清理：" .. C.c .. n .. C.x .. " --> " .. C.y .. collectgarbage("count"))
          end)
        end
      end
    end)
  
    add_trigger("otherquest_qinghuaimeng_8", "^\\s(.*)梦境掌控者\\s+(\\S+)\\((.*)\\)", function(params)
      close_trigger("otherquest_qinghuaimeng_8")
      var["killer_name"] = params[2]
      var["killer_id"] = string.lower(params[3])
      var["pfm_id"] = var["killer_id"]
      var["killer_master"] = 1    
      execute_with_optional_yun_pfm(function()
          exec("yun qi;yun jing;yun jingli")
          exec("kill " .. var["killer_id"])
      end)
    end)
  
    add_trigger("otherquest_qinghuaimeng_9", "^[ > ]*你把\\s+\"action\"\\s+设定为\\s+\"准备挑战", function(params)
      open_trigger("otherquest_qinghuaimeng_8")
      if var["killer_master"] == 1 then
        exec("look")
      end
    end)
  
    setup_hp_monitor("otherquest_qinghuaimeng_10")
  
    add_trigger("otherquest_qinghuaimeng_11", "^[ > ]*你被奖励了(.*)枚情怀币！", function(params)
      close_qinghuaimeng_triggers()
      set_end_time("情怀梦")
      var["qinghuaimeng_tongbao"] = params[1]
      log_message("完成情怀梦副本，获得" .. var["qinghuaimeng_tongbao"] .. "情怀币。", C.Y, "<解密>")
      local time_diff = calculate_time_diff("情怀梦", 1)
      log_message("任务耗时：" .. time_diff .. "。", C.Y)
      log_message("☆☆☆"..C.G.."挑战情怀梦副本结束"..C.M.."☆☆☆", C.M, "<解密>")
      reset_qinghuaimeng_timer() 
      exec("changejob")
    end)


    -- 新增触发器：处理“这里没有这个人。”，用于在无战斗时继续移动
    add_trigger("otherquest_qinghuaimeng_12", "^这里没有这个人。$", function()
        close_trigger("otherquest_qinghuaimeng_12") -- 立即关闭，避免重复触发
        if __qhm_continue_movement then
            local func_to_call = __qhm_continue_movement
            __qhm_continue_movement = nil -- 清除句柄，防止重入
            func_to_call() -- 执行下一步移动
        end
    end)

    -- 新增触发器：捕获迷宫数据 - 改进版本
    add_trigger("otherquest_qinghuaimeng_maze_capture_start1", "情怀梦境迷宫第一层：", function()
        qhm_maze_data.capturing = true
        qhm_maze_data.current_level = 1
        qhm_maze_data.raw_lines = {}
        log_message("开始捕获第一层迷宫数据...", C.Y, "<系统>")
    end)

    add_trigger("otherquest_qinghuaimeng_maze_capture_start2", "情怀梦境迷宫第二层：", function()
        qhm_maze_data.capturing = true
        qhm_maze_data.current_level = 2
        qhm_maze_data.raw_lines = {}
        log_message("开始捕获第二层迷宫数据...", C.Y, "<系统>")
    end)

    -- 捕获迷宫结束标志并保存数据
    add_trigger("otherquest_qinghuaimeng_maze_capture_end1", "情怀梦境迷宫第一层行走方向提示：", function()
        if qhm_maze_data.capturing and qhm_maze_data.current_level == 1 then
            local maze_content = table.concat(qhm_maze_data.raw_lines, "\n")
            qhm_maze_data.level1 = maze_content
            log_message("第一层迷宫数据捕获完成，共" .. #qhm_maze_data.raw_lines .. "行", C.G, "<系统>")
            qhm_maze_data.capturing = false
            qhm_maze_data.current_level = 0
            qhm_maze_data.raw_lines = {}
        end
    end)

    add_trigger("otherquest_qinghuaimeng_maze_capture_end2", "情怀梦境迷宫第二层行走方向提示：", function()
        if qhm_maze_data.capturing and qhm_maze_data.current_level == 2 then
            local maze_content = table.concat(qhm_maze_data.raw_lines, "\n")
            qhm_maze_data.level2 = maze_content
            log_message("第二层迷宫数据捕获完成，共" .. #qhm_maze_data.raw_lines .. "行", C.G, "<系统>")
            qhm_maze_data.capturing = false
            qhm_maze_data.current_level = 0
            qhm_maze_data.raw_lines = {}
        end
    end)

    -- 通用行捕获触发器 - 优先级较低
    add_trigger("otherquest_qinghuaimeng_maze_capture_line", "^(.*)$", function(params)
        if qhm_maze_data.capturing then
            local line = params[1]
            -- 跳过一些不需要的行
            if not line:match("^情怀梦境迷宫第[一二]层：") and
               not line:match("^情怀梦境迷宫第[一二]层行走方向提示：") and
               not line:match("^C:当前位置 B:宝藏位置 E:出口位置") then
                table.insert(qhm_maze_data.raw_lines, line)
            end
        end
    end)

  
    add_alias("qinghuaimeng_skills_1", function(params) setup_qinghuaimeng_skills(1) end)
    add_alias("qinghuaimeng_skills_2", function(params) setup_qinghuaimeng_skills(2) end)
  
    add_trigger("otherquest_qinghuaimeng_13", "^[ > ]*请明天再来情怀梦境吧。", function(params)
      local now_time = get_time()
      var["qinghuaimeng_start_time"] = now_time
      send("alias 挑战情怀梦 " .. now_time)
      close_qinghuaimeng_triggers()
      log_message("情怀梦副本，时间间隔不够。", C.R, "<解密>")
      reset_qinghuaimeng_timer()
      exec("changejob")
    end)    
    
    add_trigger("otherquest_qinghuaimeng_15", "^[ > ]*宝藏之地都被你破坏了，还找什么呢!", function(params)
      unset_timer("timer")
      qinghuaimeng_box = 0
      exec("mazemap")
    end)
    add_trigger("otherquest_qinghuaimeng_16", "^[ > ]*你得到了(.*)枚情怀币,但是好像也触碰到了什么机关！", function(params)
      open_trigger("otherquest_qinghuaimeng_6")
      unset_timer("timer")
      qinghuaimeng_box = 0
      var["qinghuaimeng_tongbao_1"] = params[1]
      exec("mazemap")
      log_message("挖到宝箱，获得" .. var["qinghuaimeng_tongbao_1"] .. "情怀币。", C.Y, "<解密>")
    end)
    add_trigger("otherquest_qinghuaimeng_17", "^[ > ]*你从宝藏中得到了一个(.*)。", function(params)
      var["qinghuaimeng_tongbao_2"] = params[1]
      unset_timer("timer")
      exec("mazemap")
      log_message("挖到宝箱，获得" .. var["qinghuaimeng_tongbao_2"] .. "。", C.Y, "<解密>")
    end)
    add_trigger("otherquest_qinghuaimeng_18", "^[ > ]*情怀梦境热身室", function(params)
      reset_qinghuaimeng_timer()
    end)

  end)
  
  -- 迷宫第一层寻路
  add_trigger("otherquest_qinghuaimeng_1", "s*情怀梦境迷宫第一层行走方向提示：(.*)$", function(params)
    var.qhm_enemy_present = false
    local maze = prepare_maze_data_and_parse(1)
    if not maze then return end
  
    local graph, startRoom, treasureRoom, endRoom = {}, nil, nil, nil
    local function isLetter(ch) return ch == 'B' or ch == 'C' or ch == 'E' or ch == 'O' end  
    local function isVertex(m, r, c)
        if m and m[r] and m[r][c] and isLetter(m[r][c]) then
            if not m[r][c-1] or not isLetter(m[r][c-1]) then return true end
        end
        return false
    end
  
    local idxrow, idxcol = 0, 0
    for i, row in ipairs(maze) do
        idxcol = 1
        for j, cell in ipairs(row) do
            if isVertex(maze, i, j) then
                local node_id = idxrow .. "," .. idxcol
                graph[node_id] = {}
                if cell == 'C' then startRoom = { idxrow, idxcol } end
                if cell == 'B' then treasureRoom = { idxrow, idxcol } end
                if cell == 'E' then endRoom = { idxrow, idxcol } end
                if maze[i][j+1] then
                    if isLetter(maze[i][j+1]) then
                         if maze[i][j+1] == 'C' then startRoom = { idxrow, idxcol } end
                         if maze[i][j+1] == 'B' then treasureRoom = { idxrow, idxcol } end
                         if maze[i][j+1] == 'E' then endRoom = { idxrow, idxcol } end
                    end
                end
                idxcol = idxcol + 1
            end
        end
        if i % 2 == 1 then idxrow = idxrow + 1 end
    end
    
    idxrow, idxcol = 0, 0
    for i, row in ipairs(maze) do
        idxcol = 1
        for j, cell in ipairs(row) do
            if isVertex(maze, i, j) then
                local node_id = idxrow .. "," .. idxcol
                if j > 2 and maze[i][j - 2] == '-' then table.insert(graph[node_id], idxrow .. "," .. (idxcol - 1)) end
                if j < #row and maze[i][j + 2] == '-' then table.insert(graph[node_id], idxrow .. "," .. (idxcol + 1)) end
                if i > 2 and maze[i - 1][j] == '|' then table.insert(graph[node_id], (idxrow - 1) .. "," .. idxcol) end
                if i < #maze and maze[i + 1][j] == '|' then table.insert(graph[node_id], (idxrow + 1) .. "," .. idxcol) end
                idxcol = idxcol + 1
            end
        end
        if i % 2 == 1 then idxrow = idxrow + 1 end
    end
  
    if not startRoom or not endRoom then
        echo("错误：未能在一层迷宫中找到起点(C)或终点(E)。")
        return
    end
  
    local start_node = table.concat(startRoom, ",")
    local goal_node, final_callback
    local is_going_to_exit = false
    
    if startRoom[1] == endRoom[1] and startRoom[2] == endRoom[2] then
        echo("总步数: 0"); echo("已在出口 E。")        
        check_busy2(function() wait(2, function() exec("look") end) end)
        return
    end
  
    if qinghuaimeng_box ~= 0 and treasureRoom then
        goal_node = table.concat(treasureRoom, ",")
        is_going_to_exit = false
        final_callback = function()
            echo("已到达宝藏 B!")
            var.qinghuaimeng_box = 1
            close_trigger("otherquest_qinghuaimeng_6")
            set_timer("timer", 1, function() exec("search") end)
        end
    else
        goal_node = table.concat(endRoom, ",")
        is_going_to_exit = true
        final_callback = function()
            echo("已成功到达出口 E。")            
            check_busy2(function() wait(2, function() setup_qinghuaimeng_skills(2); exec("look") end) end)
        end
    end
  
    local heuristic = function(n1, n2) local x1,y1=n1:match("(%d+),(%d+)") local x2,y2=n2:match("(%d+),(%d+)") return math.abs(x1-x2)+math.abs(y1-y2) end
    local get_neighbors = function(node) local n = {}; for _, neighbor in ipairs(graph[node] or {}) do table.insert(n, {room=neighbor, dir="", cost=1}) end return n end
    local path, _ = astar_search(start_node, goal_node, {heuristic_func=heuristic, get_neighbors_func=get_neighbors})
  
    if path then
        local directions = convert_path_to_directions(path)
        echo("总步数: " .. #directions)
        execute_maze_movement(directions, final_callback, is_going_to_exit)
    else
        echo("错误：未能为第一层迷宫找到路径。")
    end
  end)
  
  -- 迷宫第二层寻路
  add_trigger("otherquest_qinghuaimeng2_1", "s*情怀梦境迷宫第二层行走方向提示：(.*)$", function(params)
    local maze = prepare_maze_data_and_parse(2)
    if not maze then return end
  
    local startRoom_char, treasureRoom_char, endRoom_char
    for r, row in ipairs(maze) do
        for c, char in ipairs(row) do
            if char == "C" then startRoom_char = {r, c}
            elseif char == "B" then treasureRoom_char = {r, c}
            elseif char == "E" then endRoom_char = {r, c}
            end
        end
    end
    if not startRoom_char then echo("错误：未找到起点 'C'！"); return end
  
    local start_r, start_c = get_cell_coords(startRoom_char[1], startRoom_char[2])
    if not start_r then echo("错误：起点 'C' 坐标转换失败！"); return end 
    local start_node = start_r .. "," .. start_c
    
    local goal_node, final_callback, target_description
    local is_going_to_exit = false
  
    local treasure_r, treasure_c
    if treasureRoom_char then
        treasure_r, treasure_c = get_cell_coords(treasureRoom_char[1], treasureRoom_char[2])
    end
  
    if treasure_r and treasure_c then
        goal_node, target_description = treasure_r .. "," .. treasure_c, "宝藏 B"
        is_going_to_exit = false
        final_callback = function()
            echo("已到达 " .. target_description .. "!")
            var.qinghuaimeng_box = 1; var["qinghuaimeng_2"] = 0
            set_timer("timer", 1, function() exec("search") end)
        end
    else
        local end_r, end_c
        if endRoom_char then
            end_r, end_c = get_cell_coords(endRoom_char[1], endRoom_char[2])
        end
  
        if end_r and end_c then
            goal_node, target_description = end_r .. "," .. end_c, "出口 E"
            is_going_to_exit = true
            final_callback = function()
                echo("已成功到达 " .. target_description .. "。")
                check_busy2(function() wait(2, function() setup_qinghuaimeng_skills(2); exec("look") end) end)
            end
        else
            echo("错误：迷宫中未找到有效目标点 (B或E)。"); open_triggers("otherquest_qinghuaimeng", 1, 18); return
        end
    end
    echo("确定目标为: " .. target_description .. " (" .. goal_node .. ")")
  
    local heuristic = function(n1, n2) local y1,x1=n1:match("(%d+),(%d+)") local y2,x2=n2:match("(%d+),(%d+)") return math.abs(y1-y2)+math.abs(x1-x2) end
    local get_neighbors_level2 = function(node_str)
        local r_cell, c_cell = node_str:match("^(%d+),(%d+)$")
        r_cell, c_cell = tonumber(r_cell), tonumber(c_cell)
        local neighbors = {}
        local moves = {{0, 1, "east"}, {0, -1, "west"}, {1, 0, "south"}, {-1, 0, "north"}}
        for _, move in ipairs(moves) do
            local dr, dc, dir = move[1], move[2], move[3]
            local next_r, next_c = r_cell + dr, c_cell + dc
            local r_char, c_char = get_char_coords(r_cell, c_cell)
            if r_char and c_char then
                 local wall_r, wall_c = r_char + dr, c_char + (dc * 2)
                 if maze[wall_r] and maze[wall_r][wall_c] and maze[wall_r][wall_c] ~= '|' and maze[wall_r][wall_c] ~= '-' then
                     table.insert(neighbors, {room = next_r .. "," .. next_c, dir = dir, cost = 1})
                 end
            end
        end
        return neighbors
    end
    local path, _ = astar_search(start_node, goal_node, {heuristic_func=heuristic, get_neighbors_func=get_neighbors_level2, max_iterations=5000})
  
    open_triggers("otherquest_qinghuaimeng", 1, 18)
    if path then
        local directions = convert_path_to_directions(path)
        echo("总步数: " .. #directions)
        execute_maze_movement(directions, final_callback, is_going_to_exit)
    else
        echo("错误：未能为第二层迷宫找到路径。")
    end
  end)
  function close_qinghuaimeng_triggers()
    close_triggers("otherquest_qinghuaimeng", 1, 18)
    -- 关闭迷宫数据捕获触发器
    close_trigger("otherquest_qinghuaimeng_maze_capture_start1")
    close_trigger("otherquest_qinghuaimeng_maze_capture_start2")
    close_trigger("otherquest_qinghuaimeng_maze_capture_end1")
    close_trigger("otherquest_qinghuaimeng_maze_capture_end2")
    close_trigger("otherquest_qinghuaimeng_maze_capture_line")
  end
  function open_qinghuaimeng_triggers()
    open_triggers("otherquest_qinghuaimeng", 1, 18)
    -- 开启迷宫数据捕获触发器
    open_trigger("otherquest_qinghuaimeng_maze_capture_start1")
    open_trigger("otherquest_qinghuaimeng_maze_capture_start2")
    open_trigger("otherquest_qinghuaimeng_maze_capture_end1")
    open_trigger("otherquest_qinghuaimeng_maze_capture_end2")
    open_trigger("otherquest_qinghuaimeng_maze_capture_line")
  end

  -- 测试函数：显示内存中的迷宫数据状态
  add_alias("qhm_check_maze_data", function(params)
    echo("\n=== 情怀梦迷宫内存数据状态 ===")
    echo("第一层数据: " .. (qhm_maze_data.level1 and "已存储" or "未存储"))
    if qhm_maze_data.level1 then
        local lines = 0
        for _ in qhm_maze_data.level1:gmatch("[^\n]+") do lines = lines + 1 end
        echo("  - 行数: " .. lines)
    end

    echo("第二层数据: " .. (qhm_maze_data.level2 and "已存储" or "未存储"))
    if qhm_maze_data.level2 then
        local lines = 0
        for _ in qhm_maze_data.level2:gmatch("[^\n]+") do lines = lines + 1 end
        echo("  - 行数: " .. lines)
    end

    echo("当前捕获状态: " .. (qhm_maze_data.capturing and "正在捕获第" .. qhm_maze_data.current_level .. "层" or "未捕获"))
    echo("===============================")
  end)

  -- 测试函数：清空内存中的迷宫数据
  add_alias("qhm_clear_maze_data", function(params)
    qhm_maze_data.level1 = nil
    qhm_maze_data.level2 = nil
    qhm_maze_data.capturing = false
    qhm_maze_data.current_level = 0
    qhm_maze_data.raw_lines = {}
    log_message("迷宫内存数据已清空", C.Y, "<系统>")
  end)

  -- 测试函数：显示已捕获的迷宫数据内容
  add_alias("qhm_show_maze_data", function(params)
    local level = tonumber(params) or 1
    echo("\n=== 第" .. level .. "层迷宫数据内容 ===")

    local data
    if level == 1 then
        data = qhm_maze_data.level1
    elseif level == 2 then
        data = qhm_maze_data.level2
    end

    if data then
        echo("数据长度: " .. string.len(data) .. " 字符")
        echo("前10行内容:")
        local line_count = 0
        for line in data:gmatch("[^\n]+") do
            line_count = line_count + 1
            if line_count <= 10 then
                echo(line_count .. ": " .. line)
            else
                break
            end
        end
        if line_count > 10 then
            echo("... (还有 " .. (line_count - 10) .. " 行)")
        end
    else
        echo("第" .. level .. "层数据未找到")
    end
    echo("===============================")
  end)